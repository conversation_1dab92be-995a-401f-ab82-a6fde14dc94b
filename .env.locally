# 本地开发环境
NODE_ENV=development

# 资源公共路径,需要以 /开头和结尾
VITE_PUBLIC_PATH = /

# 本地开发代理，可以解决跨域及多地址代理
# 如果接口地址匹配到，则会转发到http://localhost:3000，防止本地出现跨域问题
# 可以有多个，注意多个不能换行，否则代理将会失效
VITE_PROXY = [["/dev-api","http://localhost:48080/api/admin-api"],["/upload","http://localhost:48080/api/admin-api/infra/file/upload"]]

# 是否删除Console.log
VITE_DROP_CONSOLE = false

# 基础页面地址，例如 swagger 等页面
VITE_GLOB_BASE_URL = "http://xxx"
# 接口地址,如果没有跨域问题，直接在这里配置即可
VITE_GLOB_API_URL = /dev-api

# 文件上传接口  可选
VITE_GLOB_UPLOAD_URL = /upload

# 接口地址前缀，有些系统所有接口地址都有前缀，可以在这里统一加，方便切换
VITE_GLOB_API_URL_PREFIX =

# 百度统计
VITE_APP_BAIDU_CODE = eb21166668bf766b9d059a6fd1c10777
