{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "noLib": false, "experimentalDecorators": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client"], "allowJs": true, "strict": true, "strictFunctionTypes": false, "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "dist", "removeComments": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/**/*.d.ts", "src/types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "vite.config.ts", "auto-imports.d.ts"], "exclude": ["node_modules", "dist", "public"]}