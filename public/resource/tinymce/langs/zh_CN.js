tinymce.addI18n('zh_CN', {
  'Redo': '\u91CD\u505A',
  'Undo': '\u64A4\u9500',
  'Cut': '\u526A\u5207',
  'Copy': '\u590D\u5236',
  'Paste': '\u7C98\u8D34',
  'Select all': '\u5168\u9009',
  'New document': '\u65B0\u6587\u4EF6',
  'Ok': '\u786E\u5B9A',
  'Cancel': '\u53D6\u6D88',
  'Visual aids': '\u7F51\u683C\u7EBF',
  'Bold': '\u7C97\u4F53',
  'Italic': '\u659C\u4F53',
  'Underline': '\u4E0B\u5212\u7EBF',
  'Strikethrough': '\u5220\u9664\u7EBF',
  'Superscript': '\u4E0A\u6807',
  'Subscript': '\u4E0B\u6807',
  'Clear formatting': '\u6E05\u9664\u683C\u5F0F',
  'Align left': '\u5DE6\u8FB9\u5BF9\u9F50',
  'Align center': '\u4E2D\u95F4\u5BF9\u9F50',
  'Align right': '\u53F3\u8FB9\u5BF9\u9F50',
  'Justify': '\u4E24\u7AEF\u5BF9\u9F50',
  'Bullet list': '\u9879\u76EE\u7B26\u53F7',
  'Numbered list': '\u7F16\u53F7\u5217\u8868',
  'Decrease indent': '\u51CF\u5C11\u7F29\u8FDB',
  'Increase indent': '\u589E\u52A0\u7F29\u8FDB',
  'Close': '\u5173\u95ED',
  'Formats': '\u683C\u5F0F',
  'Your browser doesn\'t support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.': '\u4F60\u7684\u6D4F\u89C8\u5668\u4E0D\u652F\u6301\u6253\u5F00\u526A\u8D34\u677F\uFF0C\u8BF7\u4F7F\u7528Ctrl+X\/C\/V\u7B49\u5FEB\u6377\u952E\u3002',
  'Headers': '\u6807\u9898',
  'Header 1': '\u6807\u98981',
  'Header 2': '\u6807\u98982',
  'Header 3': '\u6807\u98983',
  'Header 4': '\u6807\u98984',
  'Header 5': '\u6807\u98985',
  'Header 6': '\u6807\u98986',
  'Headings': '\u6807\u9898',
  'Heading 1': '\u6807\u98981',
  'Heading 2': '\u6807\u98982',
  'Heading 3': '\u6807\u98983',
  'Heading 4': '\u6807\u98984',
  'Heading 5': '\u6807\u98985',
  'Heading 6': '\u6807\u98986',
  'Preformatted': '\u9884\u5148\u683C\u5F0F\u5316\u7684',
  'Div': 'Div',
  'Pre': 'Pre',
  'Code': '\u4EE3\u7801',
  'Paragraph': '\u6BB5\u843D',
  'Blockquote': '\u5F15\u6587\u533A\u5757',
  'Inline': '\u6587\u672C',
  'Blocks': '\u57FA\u5757',
  'Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.': '\u5F53\u524D\u4E3A\u7EAF\u6587\u672C\u7C98\u8D34\u6A21\u5F0F\uFF0C\u518D\u6B21\u70B9\u51FB\u53EF\u4EE5\u56DE\u5230\u666E\u901A\u7C98\u8D34\u6A21\u5F0F\u3002',
  'Fonts': '\u5B57\u4F53',
  'Font Sizes': '\u5B57\u53F7',
  'Class': '\u7C7B\u578B',
  'Browse for an image': '\u6D4F\u89C8\u56FE\u50CF',
  'OR': '\u6216',
  'Drop an image here': '\u62D6\u653E\u4E00\u5F20\u56FE\u50CF\u81F3\u6B64',
  'Upload': '\u4E0A\u4F20',
  'Block': '\u5757',
  'Align': '\u5BF9\u9F50',
  'Default': '\u9ED8\u8BA4',
  'Circle': '\u7A7A\u5FC3\u5706',
  'Disc': '\u5B9E\u5FC3\u5706',
  'Square': '\u65B9\u5757',
  'Lower Alpha': '\u5C0F\u5199\u82F1\u6587\u5B57\u6BCD',
  'Lower Greek': '\u5C0F\u5199\u5E0C\u814A\u5B57\u6BCD',
  'Lower Roman': '\u5C0F\u5199\u7F57\u9A6C\u5B57\u6BCD',
  'Upper Alpha': '\u5927\u5199\u82F1\u6587\u5B57\u6BCD',
  'Upper Roman': '\u5927\u5199\u7F57\u9A6C\u5B57\u6BCD',
  'Anchor...': '\u951A\u70B9...',
  'Name': '\u540D\u79F0',
  'Id': '\u6807\u8BC6\u7B26',
  'Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.': '\u6807\u8BC6\u7B26\u5E94\u8BE5\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF0C\u540E\u8DDF\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u7834\u6298\u53F7\u3001\u70B9\u3001\u5192\u53F7\u6216\u4E0B\u5212\u7EBF\u3002',
  'You have unsaved changes are you sure you want to navigate away?': '\u4F60\u8FD8\u6709\u6587\u6863\u5C1A\u672A\u4FDD\u5B58\uFF0C\u786E\u5B9A\u8981\u79BB\u5F00\uFF1F',
  'Restore last draft': '\u6062\u590D\u4E0A\u6B21\u7684\u8349\u7A3F',
  'Special characters...': '\u7279\u6B8A\u5B57\u7B26...',
  'Source code': '\u6E90\u4EE3\u7801',
  'Insert\/Edit code sample': '\u63D2\u5165\/\u7F16\u8F91\u4EE3\u7801\u793A\u4F8B',
  'Language': '\u8BED\u8A00',
  'Code sample...': '\u793A\u4F8B\u4EE3\u7801...',
  'Color Picker': '\u9009\u8272\u5668',
  'R': 'R',
  'G': 'G',
  'B': 'B',
  'Left to right': '\u4ECE\u5DE6\u5230\u53F3',
  'Right to left': '\u4ECE\u53F3\u5230\u5DE6',
  'Emoticons...': '\u8868\u60C5\u7B26\u53F7...',
  'Metadata and Document Properties': '\u5143\u6570\u636E\u548C\u6587\u6863\u5C5E\u6027',
  'Title': '\u6807\u9898',
  'Keywords': '\u5173\u952E\u8BCD',
  'Description': '\u63CF\u8FF0',
  'Robots': '\u673A\u5668\u4EBA',
  'Author': '\u4F5C\u8005',
  'Encoding': '\u7F16\u7801',
  'Fullscreen': '\u5168\u5C4F',
  'Action': '\u64CD\u4F5C',
  'Shortcut': '\u5FEB\u6377\u952E',
  'Help': '\u5E2E\u52A9',
  'Address': '\u5730\u5740',
  'Focus to menubar': '\u79FB\u52A8\u7126\u70B9\u5230\u83DC\u5355\u680F',
  'Focus to toolbar': '\u79FB\u52A8\u7126\u70B9\u5230\u5DE5\u5177\u680F',
  'Focus to element path': '\u79FB\u52A8\u7126\u70B9\u5230\u5143\u7D20\u8DEF\u5F84',
  'Focus to contextual toolbar': '\u79FB\u52A8\u7126\u70B9\u5230\u4E0A\u4E0B\u6587\u83DC\u5355',
  'Insert link (if link plugin activated)': '\u63D2\u5165\u94FE\u63A5 (\u5982\u679C\u94FE\u63A5\u63D2\u4EF6\u5DF2\u6FC0\u6D3B)',
  'Save (if save plugin activated)': '\u4FDD\u5B58(\u5982\u679C\u4FDD\u5B58\u63D2\u4EF6\u5DF2\u6FC0\u6D3B)',
  'Find (if searchreplace plugin activated)': '\u67E5\u627E(\u5982\u679C\u67E5\u627E\u66FF\u6362\u63D2\u4EF6\u5DF2\u6FC0\u6D3B)',
  'Plugins installed ({0}):': '\u5DF2\u5B89\u88C5\u63D2\u4EF6 ({0}):',
  'Premium plugins:': '\u4F18\u79C0\u63D2\u4EF6\uFF1A',
  'Learn more...': '\u4E86\u89E3\u66F4\u591A...',
  'You are using {0}': '\u4F60\u6B63\u5728\u4F7F\u7528 {0}',
  'Plugins': '\u63D2\u4EF6',
  'Handy Shortcuts': '\u5FEB\u6377\u952E',
  'Horizontal line': '\u6C34\u5E73\u5206\u5272\u7EBF',
  'Insert\/edit image': '\u63D2\u5165\/\u7F16\u8F91\u56FE\u7247',
  'Image description': '\u56FE\u7247\u63CF\u8FF0',
  'Source': '\u5730\u5740',
  'Dimensions': '\u5927\u5C0F',
  'Constrain proportions': '\u4FDD\u6301\u7EB5\u6A2A\u6BD4',
  'General': '\u666E\u901A',
  'Advanced': '\u9AD8\u7EA7',
  'Style': '\u6837\u5F0F',
  'Vertical space': '\u5782\u76F4\u8FB9\u8DDD',
  'Horizontal space': '\u6C34\u5E73\u8FB9\u8DDD',
  'Border': '\u8FB9\u6846',
  'Insert image': '\u63D2\u5165\u56FE\u7247',
  'Image...': '\u56FE\u7247...',
  'Image list': '\u56FE\u7247\u5217\u8868',
  'Rotate counterclockwise': '\u9006\u65F6\u9488\u65CB\u8F6C',
  'Rotate clockwise': '\u987A\u65F6\u9488\u65CB\u8F6C',
  'Flip vertically': '\u5782\u76F4\u7FFB\u8F6C',
  'Flip horizontally': '\u6C34\u5E73\u7FFB\u8F6C',
  'Edit image': '\u7F16\u8F91\u56FE\u7247',
  'Image options': '\u56FE\u7247\u9009\u9879',
  'Zoom in': '\u653E\u5927',
  'Zoom out': '\u7F29\u5C0F',
  'Crop': '\u88C1\u526A',
  'Resize': '\u8C03\u6574\u5927\u5C0F',
  'Orientation': '\u65B9\u5411',
  'Brightness': '\u4EAE\u5EA6',
  'Sharpen': '\u9510\u5316',
  'Contrast': '\u5BF9\u6BD4\u5EA6',
  'Color levels': '\u989C\u8272\u5C42\u6B21',
  'Gamma': '\u4F3D\u9A6C\u503C',
  'Invert': '\u53CD\u8F6C',
  'Apply': '\u5E94\u7528',
  'Back': '\u540E\u9000',
  'Insert date\/time': '\u63D2\u5165\u65E5\u671F\/\u65F6\u95F4',
  'Date\/time': '\u65E5\u671F\/\u65F6\u95F4',
  'Insert\/Edit Link': '\u63D2\u5165\/\u7F16\u8F91\u94FE\u63A5',
  'Insert\/edit link': '\u63D2\u5165\/\u7F16\u8F91\u94FE\u63A5',
  'Text to display': '\u663E\u793A\u6587\u5B57',
  'Url': '\u5730\u5740',
  'Open link in...': '\u94FE\u63A5\u6253\u5F00\u4F4D\u7F6E...',
  'Current window': '\u5F53\u524D\u7A97\u53E3',
  'None': '\u65E0',
  'New window': '\u5728\u65B0\u7A97\u53E3\u6253\u5F00',
  'Remove link': '\u5220\u9664\u94FE\u63A5',
  'Anchors': '\u951A\u70B9',
  'Link...': '\u94FE\u63A5...',
  'Paste or type a link': '\u7C98\u8D34\u6216\u8F93\u5165\u94FE\u63A5',
  'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?': '\u4F60\u6240\u586B\u5199\u7684URL\u5730\u5740\u4E3A\u90AE\u4EF6\u5730\u5740\uFF0C\u9700\u8981\u52A0\u4E0Amailto:\u524D\u7F00\u5417\uFF1F',
  'The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?': '\u4F60\u6240\u586B\u5199\u7684URL\u5730\u5740\u5C5E\u4E8E\u5916\u90E8\u94FE\u63A5\uFF0C\u9700\u8981\u52A0\u4E0Ahttp:\/\/:\u524D\u7F00\u5417\uFF1F',
  'Link list': '\u94FE\u63A5\u5217\u8868',
  'Insert video': '\u63D2\u5165\u89C6\u9891',
  'Insert\/edit video': '\u63D2\u5165\/\u7F16\u8F91\u89C6\u9891',
  'Insert\/edit media': '\u63D2\u5165\/\u7F16\u8F91\u5A92\u4F53',
  'Alternative source': '\u955C\u50CF',
  'Alternative source URL': '\u66FF\u4EE3\u6765\u6E90\u7F51\u5740',
  'Media poster (Image URL)': '\u5C01\u9762(\u56FE\u7247\u5730\u5740)',
  'Paste your embed code below:': '\u5C06\u5185\u5D4C\u4EE3\u7801\u7C98\u8D34\u5728\u4E0B\u9762:',
  'Embed': '\u5185\u5D4C',
  'Media...': '\u591A\u5A92\u4F53...',
  'Nonbreaking space': '\u4E0D\u95F4\u65AD\u7A7A\u683C',
  'Page break': '\u5206\u9875\u7B26',
  'Paste as text': '\u7C98\u8D34\u4E3A\u6587\u672C',
  'Preview': '\u9884\u89C8',
  'Print...': '\u6253\u5370...',
  'Save': '\u4FDD\u5B58',
  'Find': '\u67E5\u627E',
  'Replace with': '\u66FF\u6362\u4E3A',
  'Replace': '\u66FF\u6362',
  'Replace all': '\u5168\u90E8\u66FF\u6362',
  'Previous': '\u4E0A\u4E00\u4E2A',
  'Next': '\u4E0B\u4E00\u4E2A',
  'Find and replace...': '\u67E5\u627E\u5E76\u66FF\u6362...',
  'Could not find the specified string.': '\u672A\u627E\u5230\u641C\u7D22\u5185\u5BB9.',
  'Match case': '\u533A\u5206\u5927\u5C0F\u5199',
  'Find whole words only': '\u5168\u5B57\u5339\u914D',
  'Spell check': '\u62FC\u5199\u68C0\u67E5',
  'Ignore': '\u5FFD\u7565',
  'Ignore all': '\u5168\u90E8\u5FFD\u7565',
  'Finish': '\u5B8C\u6210',
  'Add to Dictionary': '\u6DFB\u52A0\u5230\u5B57\u5178',
  'Insert table': '\u63D2\u5165\u8868\u683C',
  'Table properties': '\u8868\u683C\u5C5E\u6027',
  'Delete table': '\u5220\u9664\u8868\u683C',
  'Cell': '\u5355\u5143\u683C',
  'Row': '\u884C',
  'Column': '\u5217',
  'Cell properties': '\u5355\u5143\u683C\u5C5E\u6027',
  'Merge cells': '\u5408\u5E76\u5355\u5143\u683C',
  'Split cell': '\u62C6\u5206\u5355\u5143\u683C',
  'Insert row before': '\u5728\u4E0A\u65B9\u63D2\u5165',
  'Insert row after': '\u5728\u4E0B\u65B9\u63D2\u5165',
  'Delete row': '\u5220\u9664\u884C',
  'Row properties': '\u884C\u5C5E\u6027',
  'Cut row': '\u526A\u5207\u884C',
  'Copy row': '\u590D\u5236\u884C',
  'Paste row before': '\u7C98\u8D34\u5230\u4E0A\u65B9',
  'Paste row after': '\u7C98\u8D34\u5230\u4E0B\u65B9',
  'Insert column before': '\u5728\u5DE6\u4FA7\u63D2\u5165',
  'Insert column after': '\u5728\u53F3\u4FA7\u63D2\u5165',
  'Delete column': '\u5220\u9664\u5217',
  'Cols': '\u5217',
  'Rows': '\u884C',
  'Width': '\u5BBD',
  'Height': '\u9AD8',
  'Cell spacing': '\u5355\u5143\u683C\u5916\u95F4\u8DDD',
  'Cell padding': '\u5355\u5143\u683C\u5185\u8FB9\u8DDD',
  'Show caption': '\u663E\u793A\u6807\u9898',
  'Left': '\u5DE6\u5BF9\u9F50',
  'Center': '\u5C45\u4E2D',
  'Right': '\u53F3\u5BF9\u9F50',
  'Cell type': '\u5355\u5143\u683C\u7C7B\u578B',
  'Scope': '\u8303\u56F4',
  'Alignment': '\u5BF9\u9F50\u65B9\u5F0F',
  'H Align': '\u6C34\u5E73\u5BF9\u9F50',
  'V Align': '\u5782\u76F4\u5BF9\u9F50',
  'Top': '\u9876\u90E8\u5BF9\u9F50',
  'Middle': '\u5782\u76F4\u5C45\u4E2D',
  'Bottom': '\u5E95\u90E8\u5BF9\u9F50',
  'Header cell': '\u8868\u5934\u5355\u5143\u683C',
  'Row group': '\u884C\u7EC4',
  'Column group': '\u5217\u7EC4',
  'Row type': '\u884C\u7C7B\u578B',
  'Header': '\u8868\u5934',
  'Body': '\u8868\u4F53',
  'Footer': '\u8868\u5C3E',
  'Border color': '\u8FB9\u6846\u989C\u8272',
  'Insert template...': '\u63D2\u5165\u6A21\u677F...',
  'Templates': '\u6A21\u677F',
  'Template': '\u6A21\u677F',
  'Text color': '\u6587\u5B57\u989C\u8272',
  'Background color': '\u80CC\u666F\u8272',
  'Custom...': '\u81EA\u5B9A\u4E49...',
  'Custom color': '\u81EA\u5B9A\u4E49\u989C\u8272',
  'No color': '\u65E0',
  'Remove color': '\u79FB\u9664\u989C\u8272',
  'Table of Contents': '\u5185\u5BB9\u5217\u8868',
  'Show blocks': '\u663E\u793A\u533A\u5757\u8FB9\u6846',
  'Show invisible characters': '\u663E\u793A\u4E0D\u53EF\u89C1\u5B57\u7B26',
  'Word count': '\u5B57\u6570',
  'Words: {0}': '\u5B57\u6570\uFF1A{0}',
  '{0} words': '{0} \u5B57',
  'File': '\u6587\u4EF6',
  'Edit': '\u7F16\u8F91',
  'Insert': '\u63D2\u5165',
  'View': '\u89C6\u56FE',
  'Format': '\u683C\u5F0F',
  'Table': '\u8868\u683C',
  'Tools': '\u5DE5\u5177',
  'Powered by {0}': '\u7531{0}\u9A71\u52A8',
  'Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help': '\u5728\u7F16\u8F91\u533A\u6309ALT-F9\u6253\u5F00\u83DC\u5355\uFF0C\u6309ALT-F10\u6253\u5F00\u5DE5\u5177\u680F\uFF0C\u6309ALT-0\u67E5\u770B\u5E2E\u52A9',
  'Image title': '\u56FE\u7247\u6807\u9898',
  'Border width': '\u8FB9\u6846\u5BBD\u5EA6',
  'Border style': '\u8FB9\u6846\u6837\u5F0F',
  'Error': '\u9519\u8BEF',
  'Warn': '\u8B66\u544A',
  'Valid': '\u6709\u6548',
  'To open the popup, press Shift+Enter': '\u6309Shitf+Enter\u952E\u6253\u5F00\u5BF9\u8BDD\u6846',
  'Rich Text Area. Press ALT-0 for help.': '\u7F16\u8F91\u533A\u3002\u6309Alt+0\u952E\u6253\u5F00\u5E2E\u52A9\u3002',
  'System Font': '\u7CFB\u7EDF\u5B57\u4F53',
  'Failed to upload image: {0}': '\u56FE\u7247\u4E0A\u4F20\u5931\u8D25: {0}',
  'Failed to load plugin: {0} from url {1}': '\u63D2\u4EF6\u52A0\u8F7D\u5931\u8D25: {0} \u6765\u81EA\u94FE\u63A5 {1}',
  'Failed to load plugin url: {0}': '\u63D2\u4EF6\u52A0\u8F7D\u5931\u8D25 \u94FE\u63A5: {0}',
  'Failed to initialize plugin: {0}': '\u63D2\u4EF6\u521D\u59CB\u5316\u5931\u8D25: {0}',
  'example': '\u793A\u4F8B',
  'Search': '\u641C\u7D22',
  'All': '\u5168\u90E8',
  'Currency': '\u8D27\u5E01',
  'Text': '\u6587\u5B57',
  'Quotations': '\u5F15\u7528',
  'Mathematical': '\u6570\u5B66',
  'Extended Latin': '\u62C9\u4E01\u8BED\u6269\u5145',
  'Symbols': '\u7B26\u53F7',
  'Arrows': '\u7BAD\u5934',
  'User Defined': '\u81EA\u5B9A\u4E49',
  'dollar sign': '\u7F8E\u5143\u7B26\u53F7',
  'currency sign': '\u8D27\u5E01\u7B26\u53F7',
  'euro-currency sign': '\u6B27\u5143\u7B26\u53F7',
  'colon sign': '\u5192\u53F7',
  'cruzeiro sign': '\u514B\u9C81\u8D5B\u7F57\u5E01\u7B26\u53F7',
  'french franc sign': '\u6CD5\u90CE\u7B26\u53F7',
  'lira sign': '\u91CC\u62C9\u7B26\u53F7',
  'mill sign': '\u5BC6\u5C14\u7B26\u53F7',
  'naira sign': '\u5948\u62C9\u7B26\u53F7',
  'peseta sign': '\u6BD4\u585E\u5854\u7B26\u53F7',
  'rupee sign': '\u5362\u6BD4\u7B26\u53F7',
  'won sign': '\u97E9\u5143\u7B26\u53F7',
  'new sheqel sign': '\u65B0\u8C22\u514B\u5C14\u7B26\u53F7',
  'dong sign': '\u8D8A\u5357\u76FE\u7B26\u53F7',
  'kip sign': '\u8001\u631D\u57FA\u666E\u7B26\u53F7',
  'tugrik sign': '\u56FE\u683C\u91CC\u514B\u7B26\u53F7',
  'drachma sign': '\u5FB7\u62C9\u514B\u9A6C\u7B26\u53F7',
  'german penny symbol': '\u5FB7\u56FD\u4FBF\u58EB\u7B26\u53F7',
  'peso sign': '\u6BD4\u7D22\u7B26\u53F7',
  'guarani sign': '\u74DC\u62C9\u5C3C\u7B26\u53F7',
  'austral sign': '\u6FB3\u5143\u7B26\u53F7',
  'hryvnia sign': '\u683C\u91CC\u592B\u5C3C\u4E9A\u7B26\u53F7',
  'cedi sign': '\u585E\u5730\u7B26\u53F7',
  'livre tournois sign': '\u91CC\u5F17\u5F17\u5C14\u7B26\u53F7',
  'spesmilo sign': 'spesmilo\u7B26\u53F7',
  'tenge sign': '\u575A\u6208\u7B26\u53F7',
  'indian rupee sign': '\u5370\u5EA6\u5362\u6BD4',
  'turkish lira sign': '\u571F\u8033\u5176\u91CC\u62C9',
  'nordic mark sign': '\u5317\u6B27\u9A6C\u514B',
  'manat sign': '\u9A6C\u7EB3\u7279\u7B26\u53F7',
  'ruble sign': '\u5362\u5E03\u7B26\u53F7',
  'yen character': '\u65E5\u5143\u5B57\u6837',
  'yuan character': '\u4EBA\u6C11\u5E01\u5143\u5B57\u6837',
  'yuan character, in hong kong and taiwan': '\u5143\u5B57\u6837\uFF08\u6E2F\u53F0\u5730\u533A\uFF09',
  'yen\/yuan character variant one': '\u5143\u5B57\u6837\uFF08\u5927\u5199\uFF09',
  'Loading emoticons...': '\u52A0\u8F7D\u8868\u60C5\u7B26\u53F7...',
  'Could not load emoticons': '\u4E0D\u80FD\u52A0\u8F7D\u8868\u60C5\u7B26\u53F7',
  'People': '\u4EBA\u7C7B',
  'Animals and Nature': '\u52A8\u7269\u548C\u81EA\u7136',
  'Food and Drink': '\u98DF\u7269\u548C\u996E\u54C1',
  'Activity': '\u6D3B\u52A8',
  'Travel and Places': '\u65C5\u6E38\u548C\u5730\u70B9',
  'Objects': '\u7269\u4EF6',
  'Flags': '\u65D7\u5E1C',
  'Characters': '\u5B57\u7B26',
  'Characters (no spaces)': '\u5B57\u7B26(\u65E0\u7A7A\u683C)',
  'Error: Form submit field collision.': '\u9519\u8BEF: \u8868\u5355\u63D0\u4EA4\u5B57\u6BB5\u51B2\u7A81\u3002',
  'Error: No form element found.': '\u9519\u8BEF: \u6CA1\u6709\u8868\u5355\u63A7\u4EF6\u3002',
  'Update': '\u66F4\u65B0',
  'Color swatch': '\u989C\u8272\u6837\u672C',
  'Turquoise': '\u9752\u7EFF\u8272',
  'Green': '\u7EFF\u8272',
  'Blue': '\u84DD\u8272',
  'Purple': '\u7D2B\u8272',
  'Navy Blue': '\u6D77\u519B\u84DD',
  'Dark Turquoise': '\u6DF1\u84DD\u7EFF\u8272',
  'Dark Green': '\u6DF1\u7EFF\u8272',
  'Medium Blue': '\u4E2D\u84DD\u8272',
  'Medium Purple': '\u4E2D\u7D2B\u8272',
  'Midnight Blue': '\u6DF1\u84DD\u8272',
  'Yellow': '\u9EC4\u8272',
  'Orange': '\u6A59\u8272',
  'Red': '\u7EA2\u8272',
  'Light Gray': '\u6D45\u7070\u8272',
  'Gray': '\u7070\u8272',
  'Dark Yellow': '\u6697\u9EC4\u8272',
  'Dark Orange': '\u6DF1\u6A59\u8272',
  'Dark Red': '\u6DF1\u7EA2\u8272',
  'Medium Gray': '\u4E2D\u7070\u8272',
  'Dark Gray': '\u6DF1\u7070\u8272',
  'Black': '\u9ED1\u8272',
  'White': '\u767D\u8272',
  'Switch to or from fullscreen mode': '\u5207\u6362\u5168\u5C4F\u6A21\u5F0F',
  'Open help dialog': '\u6253\u5F00\u5E2E\u52A9\u5BF9\u8BDD\u6846',
  'history': '\u5386\u53F2',
  'styles': '\u6837\u5F0F',
  'formatting': '\u683C\u5F0F\u5316',
  'alignment': '\u5BF9\u9F50',
  'indentation': '\u7F29\u8FDB',
  'permanent pen': '\u8BB0\u53F7\u7B14',
  'comments': '\u5907\u6CE8',
  'Anchor': '\u951A\u70B9',
  'Special character': '\u7279\u6B8A\u7B26\u53F7',
  'Code sample': '\u4EE3\u7801\u793A\u4F8B',
  'Color': '\u989C\u8272',
  'Emoticons': '\u8868\u60C5',
  'Document properties': '\u6587\u6863\u5C5E\u6027',
  'Image': '\u56FE\u7247',
  'Insert link': '\u63D2\u5165\u94FE\u63A5',
  'Target': '\u6253\u5F00\u65B9\u5F0F',
  'Link': '\u94FE\u63A5',
  'Poster': '\u5C01\u9762',
  'Media': '\u5A92\u4F53',
  'Print': '\u6253\u5370',
  'Prev': '\u4E0A\u4E00\u4E2A',
  'Find and replace': '\u67E5\u627E\u548C\u66FF\u6362',
  'Whole words': '\u5168\u5B57\u5339\u914D',
  'Spellcheck': '\u62FC\u5199\u68C0\u67E5',
  'Caption': '\u6807\u9898',
  'Insert template': '\u63D2\u5165\u6A21\u677F',
})
