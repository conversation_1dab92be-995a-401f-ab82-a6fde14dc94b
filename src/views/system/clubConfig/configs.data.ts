import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE } from '@/utils/dict'
import { getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: '主键编号',
    dataIndex: 'id',
    width: 160,
    defaultHidden: true,
  },
  {
    title: '配置项名称',
    dataIndex: 'confName',
    width: 160,
  },
  {
    title: '配置项类型',
    dataIndex: 'confType',
    width: 160,
    customRender: ({ text }) => {
      const map = {
        Input: '文本',
        InputTextArea: '多行文本',
        Editor: '富文本',
        FileUpload: '图片链接',
        Switch: '状态开关',
      };
      return map[text] || text || '-';
    },
  },
  // 动态
  {
    title: '配置值',
    dataIndex: 'confVal',
    width: 300,
    customRender: ({ text, record }) => {
      console.log("text", text);
      try {
        const confType = record.confType ;

        // 根据 confType 显示不同的格式
        if (confType === 'Switch') {
          return text ? '开启' : '关闭';
        } else if (confType === 'FileUpload') {
          return value ? `图片: ${value}` : '未设置';
        } else if (confType === 'Editor') {
          return '富文本(详情中查看)';
        } else if (confType === 'InputTextArea') {
          return text ? (text.length > 50 ? text.substring(0, 50) + '...' : text) : '未设置';
        } else {
          // 默认文本类型或其他类型
          return text;
        }
      } catch {
        // 如果解析失败，直接显示原始值
        return text || '未设置';
      }
    }
  },
  {
    title: '最后修改时间',
    dataIndex: 'updateTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '配置key',
    field: 'confKey',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_CONFIG_KEY, 'string'),
      placeholder: '请选择配置key'
    },
    colProps: { span: 8 }
  },
  {
    label: '配置值',
    field: 'confVal',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
]

// 积分配置表单结构
export const pointConfigSchemas: FormSchema[] = [
  {
    label: '积分类型',
    field: 'pointType',
    component: 'Select', // 从 InputNumber 改为 Select
    required: true,
    componentProps: {
      // 使用 DICT_TYPE.MEMBER_POINT_BIZ_TYPE 字典选项
      options: getDictOptions(DICT_TYPE.MEMBER_POINT_BIZ_TYPE),
      placeholder: '请选择积分类型'
    },
    colProps: { span: 12 }
  },
  {
    label: '积分名称',
    field: 'pointName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入积分名称'
    },
    colProps: { span: 12 }
  },
  {
    label: '积分值',
    field: 'point',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      placeholder: '请输入积分值'
    },
    colProps: { span: 12 }
  },
  {
    label: '限制次数',
    field: 'limitTimes',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      placeholder: '请输入限制次数'
    },
    colProps: { span: 12 }
  },
  {
    label: '积分限制',
    field: 'pointLimit',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      placeholder: '请输入积分限制'
    },
    colProps: { span: 12 }
  }
]

// APP配置表单结构
export const appConfigSchemas: FormSchema[] = [
  {
    label: 'LOGO',
    field: 'logo',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入LOGO文件名'
    },
    colProps: { span: 12 }
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入APP名称'
    },
    colProps: { span: 12 }
  }
]

// 修改为函数形式，接收回调
export const getCreateFormSchema = (onChange?: (type: string) => void): FormSchema[] => [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '配置项类型',
    field: 'confType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '文本', value: 'Input' },
        { label: '多行文本', value: 'InputTextArea' },
        { label: '富文本', value: 'Editor' },
        { label: '图片', value: 'FileUpload' },
        { label: '状态开关', value: 'Switch' },
      ],
      placeholder: '请选择配置项类型',
      onChange: onChange || (() => { })
    },
    colProps: { span: 24 }
  },
  {
    label: '配置项名称',
    field: 'confName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置名称'
    },
    colProps: { span: 24 }
  },
  {
    label: '配置key',
    field: 'confKey',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置key',
    },
    colProps: { span: 24 }
  },
  {
    label: '配置描述',
    field: 'confDesc',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入配置描述'
    },
    colProps: { span: 24 }
  },
  // 参数值设置成动态表单
  // {
  //   label: '参数值',
  //   field: 'confVal',
  //   component: 'Input',
  //   required: true,
  //   componentProps: {
  //     placeholder: '请输入参数值'
  //   },
  //   colProps: { span: 24 }
  // }
]

export const getUpdateFormSchema = (onChange?: (type: string) => void): FormSchema[] => [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '配置key',
    field: 'confKey',
    component: 'Input',
    componentProps: {
      disabled: true
    },
    colProps: { span: 24 }
  },
  {
    label: '配置项类型',
    field: 'confType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '文本', value: 'Input' },
        { label: '多行文本', value: 'InputTextArea' },
        { label: '富文本', value: 'Editor' },
        { label: '图片', value: 'FileUpload' },
        { label: '状态开关', value: 'Switch' },
      ],
      placeholder: '请选择配置项类型',
      onChange: onChange || (() => { })
    },
    colProps: { span: 24 }
  },
  {
    label: '配置项名称',
    field: 'confName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置名称'
    },
    colProps: { span: 24 }
  },
  {
    label: '配置key',
    field: 'confKey',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置key',
    },
    colProps: { span: 24 }
  },
  {
    label: '配置描述',
    field: 'confDesc',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入配置描述'
    },
    colProps: { span: 24 }
  },
]
