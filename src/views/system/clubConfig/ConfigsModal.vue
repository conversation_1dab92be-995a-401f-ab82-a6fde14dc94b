<script lang="ts" setup>
import { nextTick, ref, unref } from 'vue';
import { appConfigSchemas, getCreateFormSchema, pointConfigSchemas, getUpdateFormSchema } from './configs.data';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@/hooks/web/useMessage';
import type { FormSchema } from '@/components/Form';
import { BasicForm, useForm } from '@/components/Form';
import { BasicModal, useModalInner } from '@/components/Modal';
import { createConfigs, getConfigs, updateConfigs } from '@/api/club/config';
import type { ComponentType } from '@/components/Form/src/types';

defineOptions({ name: 'ClubConfigsModal' });

const emit = defineEmits(['success', 'register']);

const { t } = useI18n();
const { createMessage } = useMessage();
const isUpdate = ref(true);
const currentConfType = ref('');

// 动态创建表单 schema
const createFormSchema = ref(getCreateFormSchema(handleConfTypeChange));
const updateFormSchema = ref(getUpdateFormSchema(handleConfTypeChange));
// 修复类型错误：显式定义表单配置对象
const formConfig = {
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: createFormSchema.value,
  showActionButtonGroup: false as const, // 使用 const 断言解决类型问题
  actionColOptions: { span: 23 }
};

const [registerForm, {
  setFieldsValue,
  getFieldsValue,
  resetFields,
  resetSchema,
  validate,
  appendSchemaByField,
  removeSchemaByField,
  updateSchema
}] = useForm(formConfig);

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields();
  setModalProps({ confirmLoading: false });
  isUpdate.value = !!data?.isUpdate;

  if (unref(isUpdate)) {
    // 编辑模式 - 先重置为更新表单
    currentConfType.value = '';
    updateFormSchema.value = getUpdateFormSchema(handleConfTypeChange);
    resetSchema(updateFormSchema.value);

    // 获取数据
    const res = await getConfigs(data.record.id);
    console.log("res",res);

    try {
      // 解析JSON配置值
      const confVal = res.confVal ? JSON.parse(res.confVal) : {};
      console.log("confVal", confVal);

      // 触发 confType 变化，创建动态的 confVal 字段
      if (confVal.confType) {
        handleConfTypeChange(confVal.confType);
      }

      // 等待动态字段创建完成后，一次性设置所有字段值
      await nextTick();

      setFieldsValue({
        ...res,
        ...confVal,
        confVal: confVal.confVal || confVal.confDefaultVal || ''
      });
    }
    catch (e) {
      console.error('解析 confVal 失败:', e);
      setFieldsValue(res);
    }
  }
  else {
    // 新建模式
    currentConfType.value = '';
    createFormSchema.value = getCreateFormSchema(handleConfTypeChange);
    resetSchema(createFormSchema.value);

    // 添加监听器，等待DOM更新后绑定事件
    nextTick(() => {
      // 手动触发一次初始变更（如果需要）
      const formValues = getFieldsValue();
      const initialType = formValues?.confType;
      if (initialType)
        handleConfTypeChange(initialType);
      else {
        setFieldsValue({ confType: 'Input' });
        handleConfTypeChange('Input');
      }
    });
  }
});

// 处理confType变化
function handleConfTypeChange(type: string) {
  console.log('配置Key变更:', type);
  currentConfType.value = type;
  // 先移除所有动态添加的字段
  const allFields = [...pointConfigSchemas, ...appConfigSchemas].map(item => item.field);
  removeSchemaByField(allFields);

  // 清除旧的 confVal 字段
  removeSchemaByField(['confVal']);

  const newSchema: FormSchema = {
    label: '参数值',
    field: 'confVal',
    required: true,
    component: type as ComponentType, // 保证类型一致
    componentProps: {
      placeholder: '请输入参数值'
    }
  };

  // 根据类型调整 props
  if (type === 'FileUpload') {
    newSchema.componentProps = {
      maxCount: 5,
      fileType: 'image'
    };
  }
  else if (type === 'Editor') {
    newSchema.componentProps = {
      height: 280
    };
  }
  else if (type === 'Switch') {
    newSchema.defaultValue = true;
  }

  appendSchemaByField([newSchema], 'confDesc'); // 插入到 confDesc 后面
}

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });

    // 根据confKey组合不同的JSON结构
    let confVal = {};
    confVal = {
      confKey: values.confKey,
      confName: values.confName,
      confType: values.confType,
      confVal: values.confVal,
      confDesc: values.confDesc,
    };

    const submitData = {
      id: values.id,
      confKey: values.confKey,
      confVal: JSON.stringify(confVal)
    };

    if (unref(isUpdate))
      await updateConfigs(submitData);
    else
      await createConfigs(submitData);

    closeModal();
    emit('success');
    createMessage.success(t('common.saveSuccessText'));
  }
  catch (e) {
    console.error('提交失败:', e);
    createMessage.error(t('common.saveFailText'));
  }
  finally {
    setModalProps({ confirmLoading: false });
  }
}

// 暴露方法给表单使用
defineExpose({
  handleConfKeyChange: handleConfTypeChange
});
</script>

<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? t('action.edit') : t('action.create')" @register="registerModal" width="1100px"
    @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<style scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
