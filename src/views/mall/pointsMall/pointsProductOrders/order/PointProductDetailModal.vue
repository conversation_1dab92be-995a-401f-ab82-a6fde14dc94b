<script lang="ts" setup>
import { computed, ref } from 'vue'
import { type ProductDetailInfo, orderDetailSchema, productItemSchema } from './pointProductOrderDetail.data'
import { BasicModal, useModalInner } from '@/components/Modal'
import { Description } from '@/components/Description'
import { getPointProductOrder } from '@/api/mall/pointsMall/pointsProductOrders/order'
import { getPointSubOrderByOrderId } from '@/api/mall/pointsMall/pointsProductOrders/detail'
defineOptions({ name: 'PointProductDetailModal' })

defineEmits(['register'])

const orderData = ref<Recordable>({})
// 商品详情信息列表（从订单明细接口获取）
const productDetailList = ref<ProductDetailInfo[]>([])
// 总商品数量
const totalQuantity = ref(0)

// 动态生成商品信息的 schema
const dynamicProductSchema = computed(() => {
  if (productDetailList.value.length === 0) return []

  const schema: any[] = []

  // 第一行：添加带标签的表头字段
  productItemSchema.forEach(item => {
    schema.push({
      ...item,
      field: `product_0_${item.field}`,
      render: item.render ? () => item.render!(productDetailList.value[0][item.field], productDetailList.value[0]) : () => productDetailList.value[0][item.field],
    })
  })

  // 后续行：添加不带标签的数据字段
  productDetailList.value.slice(1).forEach((product, index) => {
    productItemSchema.forEach(item => {
      schema.push({
        ...item,
        label: undefined, // 彻底移除标签
        field: `product_${index + 1}_${item.field}`,
        render: item.render ? () => item.render!(product[item.field], product) : () => product[item.field],
      })
    })
  })

  return schema
})

// 用于 Description 组件的空数据对象（因为我们使用 render 函数）
const emptyData = ref({})

const [registerModal, { setModalProps }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: false })
  if (data?.record?.id) {
    try {
      setModalProps({ loading: true })
      // 获取订单基本信息
      const orderRes = await getPointProductOrder(data.record.id)
      orderData.value = orderRes || {}

      // 获取订单明细信息（商品信息）
      const detailRes = await getPointSubOrderByOrderId(data.record.id)
      // 处理返回的商品明细列表
      if (detailRes && Array.isArray(detailRes) && detailRes.length > 0) {
        productDetailList.value = detailRes.map(detail => ({
          id: detail.id || 0,
          orderId: detail.orderId || 0,
          productId: detail.productId || 0,
          productName: detail.productName || '',
          productLogo: detail.productLogo || '',
          skuId: detail.skuId || 0,
          skuName: detail.skuName || '',
          skuLogo: detail.skuLogo || '',
          skuIntegral: detail.skuIntegral || 0,
          quantity: detail.quantity || 0,
          totalIntegral: detail.totalIntegral || 0,
          createTime: detail.createTime || '',
        }))
        // 计算总商品数量
        totalQuantity.value = productDetailList.value.reduce((sum, item) => sum + item.quantity, 0)
      }
      else {
        // 如果没有明细数据，使用空数组
        productDetailList.value = []
        totalQuantity.value = 0
      }
    }
    catch (error) {
      console.error('获取订单详情失败:', error)
    }
    finally {
      setModalProps({ loading: false })
    }
  }
})
</script>

<template>
  <BasicModal v-bind="$attrs" title="订单详情" :width="1500" :show-ok-btn="false" :show-cancel-btn="true" cancel-text="关闭"
    @register="registerModal">
    <div class="order-detail-container">
      <div class="custom-description-title">
        <span>订单信息</span>
        <span v-if="orderData.orderNo" class="order-no"> ({{ orderData.orderNo }})</span>
      </div>
      <!-- 订单详情信息 - 根据用户要求显示的字段 -->
      <Description :column="4" :bordered="true" layout="horizontal" :data="orderData" :schema="orderDetailSchema"
        class="mb-4" />

      <div class="custom-description-title">
        <span>商品信息（共 </span>
        <span class="order-no"> {{ totalQuantity }}</span>
        <span> 件商品）</span>
      </div>
      <!-- 商品信息 - 使用Description组件内部循环 -->
      <Description v-if="productDetailList.length > 0" :column="6" :bordered="true" layout="vertical" :data="emptyData"
        :schema="dynamicProductSchema" class="mb-4 compact-product-list" />
    </div>
  </BasicModal>
</template>

<style lang="less" scoped>
/* 小标题样式 */
.custom-description-title {
  font-size: 16px;
  /* 字体大小 */
  font-weight: bold;
  /* 字体加粗 */
  color: #000000d9;
  /* 字体颜色 */
  margin-bottom: 16px;
  /* 和下方组件的间距 */
}

/* 订单号的特定样式 */
.order-no {
  color: #FF0000;
  font-weight: bold;
}

/* 紧密排列商品列表样式 */
.compact-product-list {
  :deep(.ant-descriptions-item) {
    padding-bottom: 8px !important; /* 减少底部间距 */
  }

  :deep(.ant-descriptions-item-label) {
    padding-bottom: 4px !important; /* 减少标签底部间距 */
  }

  :deep(.ant-descriptions-item-content) {
    padding-bottom: 4px !important; /* 减少内容底部间距 */
  }

  /* 针对没有标签的行，进一步减少间距 */
  :deep(.ant-descriptions-item:not(:has(.ant-descriptions-item-label:not(:empty)))) {
    padding-bottom: 4px !important;
    margin-bottom: 0 !important;
  }
}
</style>
