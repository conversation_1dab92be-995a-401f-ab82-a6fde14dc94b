<script lang="ts" setup>
import { ref, unref } from 'vue'
import dayjs from 'dayjs'
import { createFormSchema, updateFormSchema } from './pointProductOrder.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { createPointProductOrder, getPointProductOrder, updatePointProductOrder } from '@/api/mall/pointsMall/pointsProductOrders/order'

defineOptions({ name: 'PointProductOrderModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()
const isUpdate = ref(true)

const [registerForm, { setFieldsValue, resetFields, resetSchema, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: createFormSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  isUpdate.value = !!data?.isUpdate
  if (unref(isUpdate)) {
    resetSchema(updateFormSchema)
    const res = await getPointProductOrder(data.record.id)

    // 处理时间字段格式转换
    const formData = { ...res }

    // 需要转换的时间字段列表
    const timeFields = [
      'orderTime', 'payTime', 'deliveryTime', 'completeTime',
      'expiredTime', 'refundTime', 'cancelTime', 'closedTime'
    ]

    // 将字符串时间转换为时间戳
    timeFields.forEach(field => {
      if (formData[field] && typeof formData[field] === 'string') {
        const timestamp = dayjs(formData[field]).valueOf()
        if (!isNaN(timestamp)) {
          formData[field] = timestamp
        }
      }
    })

    setFieldsValue(formData)
  }
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    if (unref(isUpdate))
      await updatePointProductOrder(values)
    else
      await createPointProductOrder(values)

    closeModal()
    emit('success')
    createMessage.success(t('common.saveSuccessText'))
  } finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? t('action.edit') : t('action.create')" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
