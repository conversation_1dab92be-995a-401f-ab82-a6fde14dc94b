import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: '积分名称',
    dataIndex: 'pointType',
    width: 120,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.MEMBER_POINT_BIZ_TYPE)
    },
  },
  { title: '规则说明', dataIndex: 'pointName', width: 180 },
  { title: '每次可得积分', dataIndex: 'point', width: 100 },
  { title: '获得次数', dataIndex: 'limitTimes', width: 100 },
  { title: '最大积分', dataIndex: 'pointLimit', width: 100 },
  { title: '操作时间', dataIndex: 'updateTime', width: 160 },
]
// member_point_biz_type
export const searchFormSchema: FormSchema[] = [
  {
    label: '积分名称',
    field: 'pointName',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_POINT_BIZ_TYPE, 'number'),
    },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },

  {
    label: '积分类型',
    field: 'pointType',
    component: 'Select',
    required: true,
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_POINT_BIZ_TYPE),
    },
  },
  { label: '规则说明', field: 'pointName', component: 'Input', required: true },
  { label: '每次积分', field: 'point', component: 'InputNumber', required: true, helpMessage: '每次设置为0时, 则为未指定', defaultValue: 1 },
  { label: '获得次数', field: 'limitTimes', component: 'InputNumber', required: true, helpMessage: '获得次数设置为0时, 则为无限制', defaultValue: 0 },
  { label: '积分上限', field: 'pointLimit', component: 'InputNumber', required: true, helpMessage: '积分上限设置为0时, 则为无限制', defaultValue: 0 },
]
