<script lang="ts" setup>
import { columns, searchFormSchema } from './pointsSettings.data'
import PointsRuleForm from './PointsRuleForm.vue'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { getPointConfigPage, synchronizeData } from '@/api/mall/pointsMall/pointsSettings'

defineOptions({ name: 'PointsSettings' })
const [registerModal, { openModal }] = useModal()
const [registerTable, { reload }] = useTable({
  title: '积分规则',
  api: getPointConfigPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

function DataSync() {
  synchronizeData()
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :pre-icon="IconEnum.UPLOAD" @click="DataSync" style="right: 1050px; bottom: 8px;"> 更新至俱乐部 </a-button>
        <a-button type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 新增 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'point'">
          <span v-if="record.point === 0">未指定</span>
          <span v-else>{{ record.point }}</span>
        </template>
        <template v-if="column.key === 'limitTimes'">
          <span v-if="record.limitTimes === 0">无限制</span>
          <span v-else>{{ record.limitTimes }}</span>
        </template>
        <template v-if="column.key === 'pointLimit'">
          <span v-if="record.pointLimit === 0">无限制</span>
          <span v-else>{{ record.pointLimit }}</span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              onClick: () => openModal(true, { ...record }),
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <PointsRuleForm @register="registerModal" @success="reload"/>
  </div>
</template>
