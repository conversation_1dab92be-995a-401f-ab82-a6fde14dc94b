<script lang="ts" setup>
import { columns, searchFormSchema } from './pointsSettings.data'
import PointsRuleForm from './PointsRuleForm.vue'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { getPointConfigPage,  resetToDefault} from '@/api/mall/pointsMall/pointsSettings'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({ name: 'PointsSettings' })
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const [registerTable, { reload }] = useTable({
  title: '积分规则',
  api: getPointConfigPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function restore() {
  createConfirm({
    iconType: 'warning',
    content: '确定恢复平台默认',
    async onOk() {
      try {
        await resetToDefault()
        createMessage.success('恢复成功！积分配置已恢复平台默认')
        reload()
      } catch (error) {
        console.log('恢复失败，请稍后重试')
      }
    },
  })
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :pre-icon="IconEnum.RESET" @click="restore"> 恢复平台默认 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'point'">
          <span v-if="record.point === 0">未指定</span>
          <span v-else>{{ record.point }}</span>
        </template>
        <template v-if="column.key === 'limitTimes'">
          <span v-if="record.limitTimes === 0">无限制</span>
          <span v-else>{{ record.limitTimes }}</span>
        </template>
        <template v-if="column.key === 'pointLimit'">
          <span v-if="record.pointLimit === 0">无限制</span>
          <span v-else>{{ record.pointLimit }}</span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              onClick: () => openModal(true, { ...record }),
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <PointsRuleForm @register="registerModal" @success="reload"/>
  </div>
</template>
