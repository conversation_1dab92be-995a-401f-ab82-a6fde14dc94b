<script lang="ts" setup>
import ConfigsModal from './ConfigsModal.vue'
import { columns, searchFormSchema } from './configs.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { getConfigsPage } from '@/api/club/config'
import { ref } from 'vue'

defineOptions({ name: 'ClubConfig' })

const { t } = useI18n()
const [registerModal, { openModal }] = useModal()

// 添加 ref 声明
const configsModalRef = ref()

const [registerTable, { reload }] = useTable({
  title: '俱乐部参数配置列表',
  api: async (params) => {
    const res = await getConfigsPage(params);
    console.log(res);
    const list = res?.list ?? [];
    console.log('原始数据列表:', list);
    // 映射合并 confVal 字段
    const mergedList = list.map((item) => {
      let parsedConfVal = {};
      try {
        parsedConfVal = JSON.parse(item.confVal || '{}');
      } catch {
        parsedConfVal = {};
      }
      console.log("p", parsedConfVal);
      return {
        ...item,
        ...parsedConfVal,
        confVal: parsedConfVal.confVal || parsedConfVal.confDefaultVal || ''
      };
    });

    console.log('处理后的数据列表:', mergedList)
    return {
      total: res?.total || 0,
      list: mergedList,
    };
  },
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  actionColumn: {
    width: 140,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})


function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}

</script>

<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              auth: 'club:configs:update',
              onClick: handleEdit.bind(null, record)
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <!-- 添加 ref 属性 -->
    <ConfigsModal @register="registerModal" @success="reload()" ref="configsModalRef" />
  </div>
</template>

<style scoped>
.json-preview {
  max-width: 280px;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
