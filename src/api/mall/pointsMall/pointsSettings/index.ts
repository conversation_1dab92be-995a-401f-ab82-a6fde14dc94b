import { defHttp } from '@/utils/http/axios'

/**
 * 积分设置请求参数
 */
export interface ClubPointConfigReqVO {
  /**
   * ID
   */
  id?: number
  /**
   * 积分类型：101. 注册 102. 邀请新车友完成认证 103. 认证通过  104. 参加活动 105. 商城购物 106. 发布供应（供需） 107. 发布需求（供需） 108. 完善公司信息 109. 设置群昵称 110. 设置性别  111. 设置生日 112. 设置常住地区 113。 设置故乡 114. 设置常用邮箱 115. 设置工作行业 116. 设置职务 117. 系统赠送 118. 系统扣除 119. 推荐用户
   */
  pointType: number
  /**
   * 积分名称
   */
  pointName: string
  /**
   * 每次可得积分
   */
  point: number
  /**
   * 可得次数，0为不限
   */
  limitTimes?: number
  /**
   * 最大可得积分，0为不限
   */
  pointLimit?: number
  [property: string]: any
}

/**
 * 积分设置响应参数
 */
export interface ClubPointConfigRespVO {
  /**
   * ID
   */
  id?: number
  /**
   * 积分类型
   */
  pointType?: number
  /**
   * 积分名称
   */
  pointName?: string
  /**
   * 每次可得积分
   */
  point?: number
  /**
   * 可得次数，0为不限
   */
  limitTimes?: number
  /**
   * 最大可得积分，0为不限
   */
  pointLimit?: number
  [property: string]: any
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  /**
   * 数据列表
   */
  list: T[]
  /**
   * 总量
   */
  total: number
  [property: string]: any
}

/**
 * 通用响应结构
 */
export interface CommonResult<T> {
  code?: number
  data?: T
  msg?: string
  [property: string]: any
}

/**
 * 积分设置分页查询参数
 */
export interface PointConfigPageReqVO {
  /**
   * 积分类型
   */
  pointType?: number
  /**
   * 积分名称
   */
  pointName?: string
  /**
   * 每次可得积分
   */
  point?: number
  /**
   * 可得次数，0为不限
   */
  limitTimes?: number
  /**
   * 最大可得积分，0为不限
   */
  integralLimit?: number
  /**
   * 页码，从 1 开始
   */
  pageNo: number
  /**
   * 每页条数，最大值为 100
   */
  pageSize: number
  [property: string]: any
}

/**
 * 积分设置列表查询参数
 */
export interface PointConfigListReqVO {
  /**
   * 积分类型
   */
  pointType?: number
  /**
   * 积分名称
   */
  pointName?: string
  [property: string]: any
}

/**
 * 积分设置详情查询参数
 */
export interface PointConfigDetailReqVO {
  /**
   * 积分设置ID
   */
  id: number
  [property: string]: any
}

/**
 * 删除积分设置参数
 */
export interface PointConfigDeleteReqVO {
  /**
   * 积分设置ID
   */
  id: number
  [property: string]: any
}

// ================ API 接口 ================ //

/**
 * 更新积分设置
 * @param data 积分设置数据
 */
export function updatePointConfig(data: ClubPointConfigReqVO) {
  return defHttp.put<CommonResult<boolean>>({
    url: '/club/point/config',
    data,
  })
}

/**
 * 新增积分设置
 * @param data 积分设置数据
 */
export function createPointConfig(data: ClubPointConfigReqVO) {
  return defHttp.post<CommonResult<number>>({
    url: '/club/point/config',
    data,
  })
}

/**
 * 删除积分设置
 * @param params 删除参数
 */
export function deletePointConfig(params: PointConfigDeleteReqVO) {
  return defHttp.delete<CommonResult<boolean>>({
    url: '/club/point/config',
    params,
  })
}

/**
 * 查询积分设置分页
 * @param params 查询参数
 */
export function getPointConfigPage(params: PointConfigPageReqVO) {
  return defHttp.get<CommonResult<PageResult<ClubPointConfigRespVO>>>({
    url: '/club/point/config/page',
    params,
  })
}

/**
 * 查询积分设置列表
 * @param params 查询参数
 */
export function getPointConfigList(params: PointConfigListReqVO) {
  return defHttp.get<CommonResult<ClubPointConfigRespVO[]>>({
    url: '/club/point/config/list',
    params,
  })
}

/**
 * 获取积分设置详情
 * @param params 查询参数
 */
export function getPointConfigDetail(params: PointConfigDetailReqVO) {
  return defHttp.get<CommonResult<ClubPointConfigRespVO>>({
    url: '/club/point/config/detail',
    params,
  })
}

/**
 * 积分同步俱乐部
 */
export function synchronizeData() {
  return defHttp.get({ url: '/club/point/config/synchronize-data' })
}
