import { defHttp } from '@/utils/http/axios'

// 查询网站配置列表
export function getConfigsPage(params) {
  return defHttp.get({ url: '/club/configs/page', params })
}

// 查询网站配置详情
export function getConfigs(id: number) {
  return defHttp.get({ url: `/club/configs/get?id=${id}` })
}

// 新增网站配置
export function createConfigs(data) {
  return defHttp.post({ url: '/club/configs/create', data })
}

// 修改网站配置
export function updateConfigs(data) {
  return defHttp.put({ url: '/club/configs/update', data })
}

// 删除网站配置
export function deleteConfigs(id: number) {
  return defHttp.delete({ url: `/club/configs/delete?id=${id}` })
}

// 导出网站配置 Excel
export function exportConfigs(params) {
  return defHttp.download({ url: '/club/configs/export-excel', params }, '俱乐部配置.xls')
}
