/**
 * 数据字典工具类
 */
import { ref } from 'vue'
import { useDictStoreWithOut } from '@/store/modules/dict'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  key?: any
  colorType: string
  cssClass: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export interface StringDictDataType extends DictDataType {
  value: string
}

export function getDictDatas(dictType: string) {
  return dictStore.getDictMap[dictType] || []
}

export function getDictOpts(dictType: string) {
  /**
   * 这里原来是转换类型  转换类型后反而显示不出来正确的Tag
   * 实际类型转换交给下面的getDictOptions来处理
   *
   * bugfix:
   * dictOption.push({
          ...dict,
          value: parseInt(dict.value + '')
        })
     原来的这种写法是造成页面卡死的原因
   */
  return getDictDatas(dictType)
}

export function getDictOptions(dictType: string, valueType?: 'string' | 'number' | 'boolean') {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictDatas(dictType)
  if (dictOptions && dictOptions.length > 0) {
    dictOptions.forEach((dict: DictDataType) => {
      dictOption.push({
        ...dict,
        key: dict.value,
        value:
          valueType === 'string'
            ? `${dict.value}`
            : valueType === 'boolean'
              ? `${dict.value}` === 'true'
              : Number.parseInt(`${dict.value}`),
      })
    })
  }
  return dictOption
}
export function getIntDictOptions(dictType: string): NumberDictDataType[] {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: Number.parseInt(`${dict.value}`),
    })
  })
  return dictOption
}

export function getStrDictOptions(dictType: string) {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: StringDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: `${dict.value}`,
    })
  })
  return dictOption
}

export function getBoolDictOptions(dictType: string) {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: `${dict.value}` === 'true',
    })
  })
  return dictOption
}

export function getEnabledOptions() {
  return [
    { value: true, key: true, label: '启用' },
    { value: false, key: false, label: '未启用' },
  ]
}

export function getDictObj(dictType: string, value: any) {
  const dictOptions: DictDataType[] = getDictDatas(dictType)
  if (dictOptions) {
    for (let i = 0; i < dictOptions.length; i++) {
      const dict = dictOptions[i]
      if (dict.value == value.toString())
        return dict
    }
  }
  else {
    return null
  }
}

export function getDictName(dictType: string, value: any) {
  if (!value)
    return value

  const dict = getDictObj(dictType, value)
  if (dict && dict.label)
    return dict.label
  else
    return value
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export function getDictLabel(dictType: string, value: any): string {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value)
      dictLabel.value = dict.label
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  //积分商城相关
  POINT_MALL_CATS_STATUS = "point_mall_cats_status",
  POINTMALLVIRTUAL = "pointMallVirtual",
  POINTMALLLOGISTICS = "pointMallLogistics",
  POINT_MALL_SPU_RECOMMEND = "point_mall_spu_recommend",

  // 俱乐部基础参数类型
  CLUB_ARGUMENT_TYPE = "club_argument_type",

  // ============ 新增积分商城枚举
  POINT_REFUND_STATUS = 'point_refund_status',
  POINT_POST = 'point_post',
  POINT_VIRTUAL = 'point_virtual',
  // =========
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  PORT_STATUS = 'port_status',
  BOOL_STATUS = 'sys_bool_status',
  SYSTEM_TENANT_PACKAGE_ID = 'system_tenant_package_id',
  // ======== 船票码头
  SHIP_CLASS_STATUS = 'ship_class_status',
  SHIP_AIRLINE_STATUS = 'airline_status',
  SHIP_VEHICLE_STATUS = 'vehicle_status',
  SHIP_TICKET_INDEPENDENT_STATUS = 'ship_ticket_independent_status',

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX = 'system_user_sex',
  SYSTEM_MENU_TYPE = 'system_menu_type',
  SYSTEM_ROLE_TYPE = 'system_role_type',
  SYSTEM_DATA_SCOPE = 'system_data_scope',
  SYSTEM_NOTICE_TYPE = 'system_notice_type',
  SYSTEM_OPERATE_TYPE = 'system_operate_type',
  SYSTEM_LOGIN_TYPE = 'system_login_type',
  SYSTEM_LOGIN_RESULT = 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE = 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE = 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS = 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS = 'system_sms_receive_status',
  SYSTEM_ERROR_CODE_TYPE = 'system_error_code_type',
  SYSTEM_OAUTH2_GRANT_TYPE = 'system_oauth2_grant_type',
  SYSTEM_MAIL_SEND_STATUS = 'system_mail_send_status',
  SYSTEM_NOTIFY_TEMPLATE_TYPE = 'system_notify_template_type',
  SYSTEM_FEEDBACK_SOURCE = 'system_feedback_source',

  EPL_BANK_LIST = 'epl_bank_list',

  SYSTEM_DAY_LABEL = 'system_day_label', // 星期一到星期日

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string',
  INFRA_REDIS_TIMEOUT_TYPE = 'infra_redis_timeout_type',
  INFRA_JOB_STATUS = 'infra_job_status',
  INFRA_JOB_LOG_STATUS = 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS = 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE = 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE = 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE = 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE = 'infra_codegen_scene',
  INFRA_FILE_STORAGE = 'infra_file_storage',
  INFRO_SYSTEM_CONFIG_TYPE = 'system_config_type',

  // ========== BPM 模块 ==========
  BPM_MODEL_CATEGORY = 'bpm_model_category',
  BPM_MODEL_FORM_TYPE = 'bpm_model_form_type',
  BPM_TASK_ASSIGN_RULE_TYPE = 'bpm_task_assign_rule_type',
  BPM_PROCESS_INSTANCE_STATUS = 'bpm_process_instance_status',
  BPM_PROCESS_INSTANCE_RESULT = 'bpm_process_instance_result',
  BPM_TASK_ASSIGN_SCRIPT = 'bpm_task_assign_script',
  BPM_OA_LEAVE_TYPE = 'bpm_oa_leave_type',

  // ========== PAY 模块 ==========
  PAY_CHANNEL_CODE = 'pay_channel_code', // 支付渠道编码类型
  PAY_ORDER_STATUS = 'pay_order_status', // 商家支付订单状态
  PAY_REFUND_STATUS = 'pay_refund_status', // 退款订单状态
  PAY_NOTIFY_STATUS = 'pay_notify_status', // 商家支付回调状态
  PAY_NOTIFY_TYPE = 'pay_notify_type', // 商家支付回调状态
  PAY_TRANSFER_STATUS = 'pay_transfer_status', // 转账订单状态
  PAY_TRANSFER_TYPE = 'pay_transfer_type', // 转账订单状态

  // ========== MP 模块 ==========
  MP_AUTO_REPLY_REQUEST_MATCH = 'mp_auto_reply_request_match', // 自动回复请求匹配类型
  MP_MESSAGE_TYPE = 'mp_message_type', // 消息类型

  // ========== MALL - 会员模块 ==========
  MEMBER_POINT_BIZ_TYPE = 'member_point_biz_type', // 积分的业务类型
  PRAISE_TYPE = 'praise_type', // 点赞类型

  // ========== CAR - 车型库 ==========
  CAR_BRAND_STATUS = 'car_brand_status',
  CAR_IS_OWNER = 'car_is_owner', //  是否本人名下

  // ========== MALL - 商品模块 ==========
  PRODUCT_UNIT = 'product_unit', // 商品单位
  PRODUCT_SPU_STATUS = 'product_spu_status', // 商品状态

  // ========== MALL - 交易模块 ==========
  TRADE_DELIVERY_TYPE = 'trade_delivery_type', // 配送方式
  EXPRESS_CHARGE_MODE = 'trade_delivery_express_charge_mode', // 快递的计费方式
  TRADE_AFTER_SALE_STATUS = 'trade_after_sale_status', // 售后 - 状态
  TRADE_AFTER_SALE_WAY = 'trade_after_sale_way', // 售后 - 方式
  TRADE_AFTER_SALE_TYPE = 'trade_after_sale_type', // 售后 - 类型
  TRADE_ORDER_TYPE = 'trade_order_type', // 订单 - 类型
  TRADE_ORDER_STATUS = 'trade_order_status', // 订单 - 状态
  TRADE_ORDER_ITEM_AFTER_SALE_STATUS = 'trade_order_item_after_sale_status', // 订单项 - 售后状态
  TERMINAL = 'terminal', // 终端
  BROKERAGE_ENABLED_CONDITION = 'brokerage_enabled_condition', // 分佣模式
  BROKERAGE_BIND_MODE = 'brokerage_bind_mode', // 分销关系绑定模式
  BROKERAGE_BANK_NAME = 'brokerage_bank_name', // 佣金提现银行
  BROKERAGE_WITHDRAW_TYPE = 'brokerage_withdraw_type', // 佣金提现类型
  BROKERAGE_RECORD_BIZ_TYPE = 'brokerage_record_biz_type', // 佣金业务类型
  BROKERAGE_RECORD_STATUS = 'brokerage_record_status', // 佣金状态
  BROKERAGE_WITHDRAW_STATUS = 'brokerage_withdraw_status', // 佣金提现状态
  PROMOTION_BARGAIN_RECORD_STATUS = 'promotion_bargain_record_status', // 砍价记录的状态

  // ========== MALL - 营销模块 ==========
  PROMOTION_DISCOUNT_TYPE = 'promotion_discount_type', // 优惠类型
  PROMOTION_PRODUCT_SCOPE = 'promotion_product_scope', // 营销的商品范围
  PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE = 'promotion_coupon_template_validity_type', // 优惠劵模板的有限期类型
  PROMOTION_COUPON_STATUS = 'promotion_coupon_status', // 优惠劵的状态
  PROMOTION_COUPON_TAKE_TYPE = 'promotion_coupon_take_type', // 优惠劵的领取方式
  PROMOTION_ACTIVITY_STATUS = 'promotion_activity_status', // 优惠活动的状态
  PROMOTION_CONDITION_TYPE = 'promotion_condition_type', // 营销的条件类型枚举
  PROMOTION_COMBINATION_RECORD_STATUS = 'promotion_combination_record_status', // 拼团记录的状态
  PROMOTION_BANNER_POSITION = 'promotion_banner_position', // banner 定位

  // ========== IOT - 物联网模块 ==========
  IOT_PRODUCT_NET_TYPE = 'iot_product_net_type',
  IOT_PRODUCT_NODE_TYPE = 'iot_product_node_type',
  IOT_PRODUCT_SHARE_METHOD = 'iot_product_share_method',
  IOT_PRODUCT_FUNCTION_TYPE = 'iot_pruduct_function_type',
  IOT_PRODUCT_DATA_TYPE = 'iot_product_data_type',

  //
  APP_INFO_VERSION_PLATFORM = 'app_info_version_platform',

  SITE_ARTICLE_TYPE = 'site_article_type',
  SITE_BANNER_TYPE = 'site_banner_type',
  SITE_SERVICE_TYPE = 'site_service_type',
  // 文章内容关联类型
  ARTICLE_ASSOCIATED_TYPE = 'article_associated_type',
  // 系统消息关联类型
  SYS_MESSAGE_ASSOCIATED_TYPE = 'sys_message_associated_type',
  // 轮播图关联类型
  BANNER_ASSOCIATED_TYPE = 'banner_associated_type',

  // ECU 服务工单模块
  ECU_MERCHANT_LEVEL = 'ecu_merchant_level',
  ECU_WORK_ORDER_STATUS = 'ecu_work_order_status',
  ECU_WORK_ORDER_TYPE = 'ecu_work_order_type',
  ECU_DATA_PROCESS_TYPE = 'ecu_data_process_type',
  ECU_CALIBRATION_PROJECT_TYPE = 'ecu_calibration_project_type',

  ECU_PV_APPLY_STATUS = 'ecu_pv_apply_status',
  ECU_PV_OIL_TYPE = 'ecu_pv_oil_type',
  ECU_PV_TRANSMISSION_TYPE = 'ecu_pv_transmission_type',
  ECU_CLIENT_TYPE = 'ecu_client_type',

  // ========== 平台数据 ==========
  LABEL_TYPE = 'label_type', // 标签类型

  // ========== 商家模块 ==========
  MERCHANT_TYPE = 'merchant_type',
  SCENIC_SPOT_STATUS = 'scenic_spot_status', // 景区状态
  SCENIC_SPOT_TICKET_TYPE = 'scenic_spot_ticket_type', // 景区门票类型
  SCENIC_SPOT_TICKET_STATUS = 'scenic_spot_ticket_status', // 景区门票状态
  TICKET_SPECS_NAME = 'ticket_specs_name', // 门票规格
  HOTEL_STATUS = 'hotel_status', // 酒店状态
  HOTEL_STAR = 'hotel_star', // 酒店星级
  HOTEL_TYPE = 'hotel_types', // 酒店星级
  ENABLE_HOT_SPOT_PUSH = 'enable_hot_spot_push', // 是否推送热门景点

  FOOD_CATEGORY_STATUS = 'food_category_status',

  HOTEL_ORDER_STATUS = 'hotel_order_status', // 酒店订单状态
  SCENIC_SPOT_ORDER_STATUS = 'scenic_spot_order_status', // 景区状态

  HOUSE_STATUS = 'house_status', // 酒店房型状态
  HOUSE_DATE_TYPE = 'house_date_type', // 酒店房型日期类型

  // ========== 会员 ==========
  MEMBER_STATUS = 'sys_member_status', // 会员状态
  MEMBER_AUTH_STATUS = 'member_auth_status', // 认证状态
  MEMBER_JOIN_STATUS = 'member_join_status', // 入群状态
  SYSTEM_SOCIAL_TYPE = 'system_social_type',

  VISIT_TYPE = 'visit_type',//资源浏览类型
  CLUBID_IS_USED = 'clubid_is_used', //使用状态
  CLUBID_KEEP_TYPE = 'clubid_keep_type',//预留类型
  CLUBID_IS_OPEN = 'cllubid_is_open',//开放状态

  // ======== 俱乐部活动 ========

  CLUB_ACTIVITY_CONSUME_TYPE = 'club_activity_consume_type',
  CLUB_ACTIVITY_PUBLISH_STATUS = 'club_activity_publish_status',
  CLUB_ACTIVITY_TYPE = 'club_activity_type',
  CLUB_ACTIVITY_STATUS = 'club_activity_status',
  CLUB_ACTIVITY_ENTROLL_STATUS = 'club_activity_enroll_status',

  // 报名

  CLUB_ACTIVITY_ENROLL_SIGN_STATUS = 'club_activity_enroll_sign_status', // 俱乐部活动报名签到状态
  CLUB_ACTIVITY_ENROLL_REFUND_STATUS = 'club_activity_enroll_refund_status', // 俱乐部活动报名退款状态
  CLUB_ACTIVITY_ENROLL_PAYMENT_STATUS = 'club_activity_enroll_payment_status', // 俱乐部活动报名支付状态
  CLUB_ACTIVITY_ENROLL_FORM_CONFIGS = 'club_activity_enroll_form_configs',
  SETTLING_STATUS = 'settling_status',

  CLUB_CONFIG_KEY = 'club_config_key',
  CLUB_TAG_TYPE = 'club_tag_type',
}
