# 开发环境：本地只启动前端项目，依赖开发环境（后端、APP）
NODE_ENV=production

VITE_DEV=true

# 请求路径
VITE_BASE_URL='https://basic.lingmaoshuke.com/'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server
# 上传路径
VITE_UPLOAD_URL='https://basic.lingmaoshuke.com/api/admin-api/infra/file/upload'

# 接口地址
VITE_API_URL=/api/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/console-elem/

# 输出路径
VITE_OUT_DIR=dist

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='https://basic.lingmaoshuke.com/h5'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true
