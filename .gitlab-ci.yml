variables:

# 定义工作状态
stages:
  - build

# Dev ---------------------------------------------------------------------------------------------------
build-job-dev:
  only:
    - master-lingmao-basic
  stage: build
  tags:
    - ruoyi-dev
  image: node:18.18.0
  script:
    - ls -al
    - rm -rf yarn.lock
    - yarn config set registry https://registry.npm.taobao.org
    - yarn config set "strict-ssl" false
    - yarn install
    - yarn run build:dev-ligmao
    - ls -al
    - mkdir -p /webapp/ruoyi-admin-antd
    - cp -rf dist/* /webapp/ruoyi-admin-antd


# carclub-saas ---------------------------------------------------------------------------------------------------
build-job-carclub-saas:
  only:
    - car-club-saas-service-from-basic
  stage: build
  tags:
    - ruoyi-dev
  image: node:18.18.0
  script:
    - ls -al
    - rm -rf yarn.lock
    - yarn config set registry https://registry.npm.taobao.org
    - yarn config set "strict-ssl" false
    - npm install pnpm -g
    - pnpm install
    - yarn run build:carclub-saas
    - ls -al
    - mkdir -p /webapp/car-club-saas-webapp-console-antd
    - cp -rf dist/* /webapp/car-club-saas-webapp-console-antd
